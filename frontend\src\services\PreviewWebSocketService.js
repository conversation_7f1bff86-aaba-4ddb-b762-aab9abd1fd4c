import WebSocketService from './WebSocketService';

/**
 * Enhanced WebSocket service for real-time preview synchronization
 * Handles collaborative editing, preview state sync, and real-time updates
 */
class PreviewWebSocketService extends WebSocketService {
  constructor(options = {}) {
    super(options);

    // Preview-specific state
    this.previewState = {
      components: new Map(),
      deviceSettings: {},
      viewportState: {},
      collaborators: new Map(),
      cursors: new Map()
    };

    // Event handlers for preview-specific events
    this.previewHandlers = new Map();

    // Debounce settings for different update types
    this.debounceSettings = {
      componentUpdate: 300,
      deviceChange: 100,
      cursorMove: 50,
      viewportChange: 200
    };

    // Initialize preview-specific message handlers
    this.initializePreviewHandlers();
  }

  /**
   * Initialize preview-specific WebSocket message handlers
   */
  initializePreviewHandlers() {
    // Component update handlers
    this.on('component_updated', this.handleComponentUpdate.bind(this));
    this.on('component_added', this.handleComponentAdd.bind(this));
    this.on('component_deleted', this.handleComponentDelete.bind(this));
    this.on('component_moved', this.handleComponentMove.bind(this));

    // Device and viewport handlers
    this.on('device_changed', this.handleDeviceChange.bind(this));
    this.on('viewport_changed', this.handleViewportChange.bind(this));

    // Collaboration handlers
    this.on('collaborator_joined', this.handleCollaboratorJoin.bind(this));
    this.on('collaborator_left', this.handleCollaboratorLeave.bind(this));
    this.on('cursor_moved', this.handleCursorMove.bind(this));
    this.on('selection_changed', this.handleSelectionChange.bind(this));

    // Preview state sync
    this.on('preview_state_sync', this.handlePreviewStateSync.bind(this));
    this.on('preview_state_request', this.handlePreviewStateRequest.bind(this));
  }

  /**
   * Send component update with real-time synchronization
   */
  async sendComponentUpdate(componentId, componentData, options = {}) {
    const message = {
      type: 'component_update',
      component_id: componentId,
      component_data: componentData,
      timestamp: new Date().toISOString(),
      user_id: options.userId || 'anonymous',
      session_id: options.sessionId,
      immediate: options.immediate || false
    };

    // Update local state immediately for optimistic updates
    if (options.immediate) {
      this.previewState.components.set(componentId, componentData);
      this.triggerPreviewEvent('component_updated_local', message);
    }

    return this.send(message, {
      priority: options.immediate ? 'high' : 'normal',
      compress: true
    });
  }

  /**
   * Send device change notification
   */
  async sendDeviceChange(deviceType, deviceConfig, options = {}) {
    const message = {
      type: 'device_change',
      device_type: deviceType,
      device_config: deviceConfig,
      timestamp: new Date().toISOString(),
      user_id: options.userId || 'anonymous',
      session_id: options.sessionId
    };

    // Update local device state
    this.previewState.deviceSettings = {
      type: deviceType,
      config: deviceConfig,
      timestamp: new Date()
    };

    return this.send(message, { compress: false });
  }

  /**
   * Send cursor position for collaborative editing
   */
  async sendCursorPosition(position, options = {}) {
    const message = {
      type: 'cursor_move',
      position: position,
      timestamp: new Date().toISOString(),
      user_id: options.userId || 'anonymous',
      session_id: options.sessionId
    };

    return this.send(message, {
      priority: 'low',
      compress: false,
      throttle: this.debounceSettings.cursorMove
    });
  }

  /**
   * Send viewport change notification
   */
  async sendViewportChange(viewport, options = {}) {
    const message = {
      type: 'viewport_change',
      viewport: viewport,
      timestamp: new Date().toISOString(),
      user_id: options.userId || 'anonymous',
      session_id: options.sessionId
    };

    // Update local viewport state
    this.previewState.viewportState = {
      ...viewport,
      timestamp: new Date()
    };

    return this.send(message, { compress: true });
  }

  /**
   * Request current preview state from server
   */
  async requestPreviewState(sessionId) {
    return this.send({
      type: 'preview_state_request',
      session_id: sessionId,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Sync preview state with other collaborators
   */
  async syncPreviewState(sessionId) {
    const state = {
      components: Array.from(this.previewState.components.entries()),
      deviceSettings: this.previewState.deviceSettings,
      viewportState: this.previewState.viewportState,
      timestamp: new Date().toISOString()
    };

    return this.send({
      type: 'preview_state_sync',
      session_id: sessionId,
      state: state
    }, { compress: true });
  }

  // Message handlers
  handleComponentUpdate(data) {
    const { component_id, component_data, user_id, timestamp } = data;

    // Update local state
    this.previewState.components.set(component_id, {
      ...component_data,
      lastUpdatedBy: user_id,
      lastUpdated: timestamp
    });

    // Trigger preview event
    this.triggerPreviewEvent('component_updated', data);
  }

  handleComponentAdd(data) {
    const { component } = data;

    if (component && component.id) {
      this.previewState.components.set(component.id, component);
      this.triggerPreviewEvent('component_added', data);
    }
  }

  handleComponentDelete(data) {
    const { component_id } = data;

    if (component_id) {
      this.previewState.components.delete(component_id);
      this.triggerPreviewEvent('component_deleted', data);
    }
  }

  handleComponentMove(data) {
    const { component_id, new_position } = data;

    const component = this.previewState.components.get(component_id);
    if (component) {
      this.previewState.components.set(component_id, {
        ...component,
        position: new_position
      });
      this.triggerPreviewEvent('component_moved', data);
    }
  }

  handleDeviceChange(data) {
    const { device_type, device_config, user_id } = data;

    this.previewState.deviceSettings = {
      type: device_type,
      config: device_config,
      changedBy: user_id,
      timestamp: new Date()
    };

    this.triggerPreviewEvent('device_changed', data);
  }

  handleViewportChange(data) {
    const { viewport, user_id } = data;

    this.previewState.viewportState = {
      ...viewport,
      changedBy: user_id,
      timestamp: new Date()
    };

    this.triggerPreviewEvent('viewport_changed', data);
  }

  handleCollaboratorJoin(data) {
    const { user_id, username, avatar } = data;

    this.previewState.collaborators.set(user_id, {
      username,
      avatar,
      joinedAt: new Date(),
      isActive: true
    });

    this.triggerPreviewEvent('collaborator_joined', data);
  }

  handleCollaboratorLeave(data) {
    const { user_id } = data;

    this.previewState.collaborators.delete(user_id);
    this.previewState.cursors.delete(user_id);

    this.triggerPreviewEvent('collaborator_left', data);
  }

  handleCursorMove(data) {
    const { user_id, position } = data;

    this.previewState.cursors.set(user_id, {
      position,
      timestamp: new Date()
    });

    this.triggerPreviewEvent('cursor_moved', data);
  }

  handleSelectionChange(data) {
    const { user_id, selection } = data;

    const collaborator = this.previewState.collaborators.get(user_id);
    if (collaborator) {
      this.previewState.collaborators.set(user_id, {
        ...collaborator,
        selection,
        lastActivity: new Date()
      });
    }

    this.triggerPreviewEvent('selection_changed', data);
  }

  handlePreviewStateSync(data) {
    const { state } = data;

    if (state) {
      // Update components
      if (state.components) {
        this.previewState.components.clear();
        state.components.forEach(([id, component]) => {
          this.previewState.components.set(id, component);
        });
      }

      // Update device settings
      if (state.deviceSettings) {
        this.previewState.deviceSettings = state.deviceSettings;
      }

      // Update viewport state
      if (state.viewportState) {
        this.previewState.viewportState = state.viewportState;
      }
    }

    this.triggerPreviewEvent('preview_state_synced', data);
  }

  handlePreviewStateRequest(data) {
    // Respond with current state
    this.syncPreviewState(data.session_id);
  }

  /**
   * Trigger preview-specific events
   */
  triggerPreviewEvent(eventType, data) {
    const handlers = this.previewHandlers.get(eventType) || [];
    handlers.forEach(handler => {
      try {
        handler(data);
      } catch (error) {
        console.error(`Error in preview event handler for ${eventType}:`, error);
      }
    });
  }

  /**
   * Add preview event listener
   */
  onPreviewEvent(eventType, handler) {
    if (!this.previewHandlers.has(eventType)) {
      this.previewHandlers.set(eventType, []);
    }
    this.previewHandlers.get(eventType).push(handler);

    return () => {
      const handlers = this.previewHandlers.get(eventType) || [];
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    };
  }

  /**
   * Get current preview state
   */
  getPreviewState() {
    return {
      components: Array.from(this.previewState.components.entries()),
      deviceSettings: this.previewState.deviceSettings,
      viewportState: this.previewState.viewportState,
      collaborators: Array.from(this.previewState.collaborators.entries()),
      cursors: Array.from(this.previewState.cursors.entries())
    };
  }

  /**
   * Clear preview state
   */
  clearPreviewState() {
    this.previewState.components.clear();
    this.previewState.collaborators.clear();
    this.previewState.cursors.clear();
    this.previewState.deviceSettings = {};
    this.previewState.viewportState = {};
  }

  /**
   * Join a collaborative session
   */
  async joinSession(sessionId, userInfo = {}) {
    return this.send({
      type: 'join_session',
      session_id: sessionId,
      user_info: {
        username: userInfo.username || 'Anonymous',
        avatar: userInfo.avatar || null,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Leave a collaborative session
   */
  async leaveSession(sessionId) {
    return this.send({
      type: 'leave_session',
      session_id: sessionId,
      timestamp: new Date().toISOString()
    });
  }
}

export default PreviewWebSocketService;
